/**
 * 主应用程序组件
 */
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import AppLayout from './components/Layout/AppLayout';
import DeviceList from './components/Devices/DeviceList';
import TelemetryView from './components/Telemetry/TelemetryView';
import SystemInfo from './components/System/SystemInfo';
import Settings from './components/Settings/Settings';

// 创建主题
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <AppLayout>
          <Routes>
            <Route path="/" element={<DeviceList />} />
            <Route path="/devices" element={<DeviceList />} />
            <Route path="/telemetry" element={<TelemetryView />} />
            <Route path="/system" element={<SystemInfo />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </AppLayout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
