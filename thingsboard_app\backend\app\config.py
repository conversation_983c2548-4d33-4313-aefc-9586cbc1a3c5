"""
应用程序配置管理
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用程序设置"""
    
    # 应用基础配置
    app_name: str = Field(default="ThingsBoard Full-Stack App", description="应用程序名称")
    app_version: str = Field(default="1.0.0", description="应用程序版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    
    # ThingsBoard配置
    thingsboard_url: str = Field(default="http://**************:8080", description="ThingsBoard服务器地址")
    thingsboard_username: str = Field(default="<EMAIL>", description="ThingsBoard用户名")
    thingsboard_password: str = Field(default="tenant2025a", description="ThingsBoard密码")

    # 安全配置
    secret_key: str = Field(default="tb-app-secret-key-2025-windows-local-dev", description="JWT密钥")
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间（分钟）")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379", description="Redis连接地址")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    
    # 数据库配置（可选，用于本地数据存储）
    database_url: Optional[str] = Field(default=None, description="数据库连接地址")
    
    # CORS配置
    cors_origins: list = Field(default=["http://localhost:3000"], description="允许的CORS源")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    
    # 缓存配置
    cache_ttl: int = Field(default=300, description="缓存TTL（秒）")
    
    # 分页配置
    default_page_size: int = Field(default=20, description="默认分页大小")
    max_page_size: int = Field(default=100, description="最大分页大小")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局设置实例
_settings = None


def get_settings() -> Settings:
    """获取设置实例（延迟初始化）"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


# 环境检查
def validate_environment():
    """验证环境配置"""
    # 由于我们已经设置了默认值，不再强制要求环境变量
    # 只验证配置是否能正确加载
    try:
        settings = get_settings()
        # 验证关键配置不为空
        if not settings.thingsboard_url:
            raise ValueError("ThingsBoard URL 不能为空")
        if not settings.thingsboard_username:
            raise ValueError("ThingsBoard 用户名不能为空")
        if not settings.thingsboard_password:
            raise ValueError("ThingsBoard 密码不能为空")
        if not settings.secret_key:
            raise ValueError("Secret Key 不能为空")
    except Exception as e:
        raise ValueError(f"配置验证失败: {e}")


# 日志配置
import logging
import sys

def setup_logging():
    """设置日志配置"""
    settings = get_settings()
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )

    # 如果指定了日志文件，添加文件处理器
    if settings.log_file:
        file_handler = logging.FileHandler(settings.log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("tb_rest_client").setLevel(logging.INFO)


# 初始化配置
def init_config():
    """初始化配置"""
    validate_environment()
    setup_logging()

    settings = get_settings()
    logger = logging.getLogger(__name__)
    logger.info(f"应用程序启动: {settings.app_name} v{settings.app_version}")
    logger.info(f"ThingsBoard URL: {settings.thingsboard_url}")
    logger.info(f"调试模式: {settings.debug}")
