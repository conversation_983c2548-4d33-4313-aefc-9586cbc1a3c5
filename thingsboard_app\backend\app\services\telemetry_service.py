"""
遥测数据服务
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from tb_rest_client.rest import ApiException
from ..models.telemetry import (
    TelemetryRequest, SaveTelemetryRequest, SaveAttributesRequest,
    LatestTelemetry, TimeseriesData, AttributeData
)
from .thingsboard_service import ThingsBoardService

logger = logging.getLogger(__name__)


class TelemetryService(ThingsBoardService):
    """遥测数据服务"""
    
    def get_latest_telemetry(self, entity_id: str, keys: Optional[List[str]] = None) -> dict:
        """获取最新遥测数据"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": entity_id}

            # 获取最新遥测数据
            keys_str = ",".join(keys) if keys else None
            telemetry_data = client.get_latest_timeseries(
                entity_id=entity_id_obj,
                keys=keys_str,
                use_strict_data_types=True
            )

            # 转换数据格式
            latest_data = {}
            if telemetry_data:
                for key, data_list in telemetry_data.items():
                    if data_list:
                        latest_point = data_list[0]  # 最新的数据点
                        latest_data[key] = {
                            "value": latest_point["value"],
                            "ts": latest_point["ts"],
                            "key": key
                        }

            return {
                "success": True,
                "data": latest_data
            }
        except ApiException as e:
            # 如果是404错误（设备不存在或没有数据），返回空数据
            if hasattr(e, 'status') and e.status == 404:
                logger.info(f"设备 {entity_id} 没有最新遥测数据")
                return {
                    "success": True,
                    "data": {}
                }
            return self.handle_api_exception(e, "获取最新遥测数据")
        except Exception as e:
            logger.warning(f"获取设备 {entity_id} 最新遥测数据时发生异常: {e}")
            # 对于其他异常，也返回空数据而不是错误
            return {
                "success": True,
                "data": {}
            }
    
    def get_timeseries_data(self, request: TelemetryRequest) -> dict:
        """获取时间序列数据"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": request.entity_id}
            
            # 获取时间序列数据
            keys_str = ",".join(request.keys) if request.keys else None
            timeseries_data = client.get_timeseries(
                entity_id=entity_id_obj,
                keys=keys_str,
                start_ts=request.start_time,
                end_ts=request.end_time,
                interval=request.interval,
                limit=request.limit,
                agg=request.agg,
                use_strict_data_types=request.use_strict_data_types
            )
            
            # 转换数据格式
            series_data = {}
            for key, data_list in timeseries_data.items():
                series_data[key] = [
                    {
                        "ts": point["ts"],
                        "value": point["value"]
                    }
                    for point in data_list
                ]
            
            return {
                "success": True,
                "data": {
                    "series": series_data,
                    "entity_id": request.entity_id,
                    "keys": request.keys,
                    "start_time": request.start_time,
                    "end_time": request.end_time
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取时间序列数据")
        except Exception as e:
            return self.handle_exception(e, "获取时间序列数据")
    
    def save_telemetry(self, request: SaveTelemetryRequest) -> dict:
        """保存遥测数据"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": request.entity_id}
            
            # 保存遥测数据
            if request.ttl:
                client.save_entity_telemetry_with_ttl(
                    entity_id=entity_id_obj,
                    telemetry=request.telemetry,
                    ttl=request.ttl
                )
            else:
                client.save_entity_telemetry(
                    entity_id=entity_id_obj,
                    telemetry=request.telemetry,
                    scope=request.scope
                )
            
            return {
                "success": True,
                "message": "遥测数据保存成功",
                "data": {
                    "entity_id": request.entity_id,
                    "telemetry_count": len(request.telemetry)
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "保存遥测数据")
        except Exception as e:
            return self.handle_exception(e, "保存遥测数据")
    
    def get_attributes(self, entity_id: str, scope: str, keys: Optional[List[str]] = None) -> dict:
        """获取属性数据"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": entity_id}
            
            # 获取属性数据
            keys_str = ",".join(keys) if keys else None
            attributes = client.get_attributes(
                entity_id=entity_id_obj,
                keys=keys_str,
                scope=scope
            )
            
            return {
                "success": True,
                "data": attributes
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取属性数据")
        except Exception as e:
            return self.handle_exception(e, "获取属性数据")
    
    def save_attributes(self, request: SaveAttributesRequest) -> dict:
        """保存属性数据"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": request.entity_id}
            
            # 保存属性数据
            client.save_entity_attributes_v2(
                entity_id=entity_id_obj,
                attributes=request.attributes,
                scope=request.scope
            )
            
            return {
                "success": True,
                "message": "属性数据保存成功",
                "data": {
                    "entity_id": request.entity_id,
                    "attributes_count": len(request.attributes)
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "保存属性数据")
        except Exception as e:
            return self.handle_exception(e, "保存属性数据")
    
    def get_telemetry_keys(self, entity_id: str) -> dict:
        """获取遥测数据键列表"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": entity_id}

            # 获取遥测数据键
            keys = client.get_timeseries_keys_v1(entity_id=entity_id_obj)

            # 确保返回的是列表
            if keys is None:
                keys = []
            elif not isinstance(keys, list):
                keys = list(keys) if keys else []

            return {
                "success": True,
                "data": keys
            }
        except ApiException as e:
            # 如果是404错误（设备不存在或没有数据），返回空列表
            if hasattr(e, 'status') and e.status == 404:
                logger.info(f"设备 {entity_id} 没有遥测数据键")
                return {
                    "success": True,
                    "data": []
                }
            return self.handle_api_exception(e, "获取遥测数据键")
        except Exception as e:
            logger.warning(f"获取设备 {entity_id} 遥测数据键时发生异常: {e}")
            # 对于其他异常，也返回空列表而不是错误
            return {
                "success": True,
                "data": []
            }
    
    def get_attribute_keys(self, entity_id: str) -> dict:
        """获取属性键列表"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": entity_id}

            # 获取属性键
            keys = client.get_attribute_keys(entity_id=entity_id_obj)

            return {
                "success": True,
                "data": keys
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取属性键")
        except Exception as e:
            return self.handle_exception(e, "获取属性键")


    
    def delete_timeseries(self, entity_id: str, keys: List[str], 
                         start_time: Optional[int] = None, 
                         end_time: Optional[int] = None) -> dict:
        """删除时间序列数据"""
        try:
            client = self.get_authenticated_client()
            # 构建实体ID对象
            entity_id_obj = {"entityType": "DEVICE", "id": entity_id}
            
            # 删除时间序列数据
            keys_str = ",".join(keys)
            client.delete_entity_timeseries(
                entity_id=entity_id_obj,
                keys=keys_str,
                start_ts=start_time,
                end_ts=end_time,
                rewrite_latest_if_deleted=True
            )
            
            return {
                "success": True,
                "message": f"时间序列数据删除成功，键: {keys_str}",
                "data": {
                    "entity_id": entity_id,
                    "deleted_keys": keys,
                    "start_time": start_time,
                    "end_time": end_time
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "删除时间序列数据")
        except Exception as e:
            return self.handle_exception(e, "删除时间序列数据")


# 全局服务实例
_telemetry_service = None


def get_telemetry_service() -> TelemetryService:
    """获取遥测服务实例（单例模式）"""
    global _telemetry_service
    if _telemetry_service is None:
        _telemetry_service = TelemetryService()
    return _telemetry_service
