"""
设备管理服务
"""
import logging
from typing import List, Dict, Any, Optional
from tb_rest_client.rest import ApiException
from tb_rest_client.models.models_ce import Device, DeviceProfile, DeviceCredentials
from ..models.device import (
    CreateDeviceRequest, UpdateDeviceRequest, DeviceSearchParams,
    DeviceInfo, DeviceStatus, DeviceStatistics
)
from .thingsboard_service import ThingsBoardService

logger = logging.getLogger(__name__)


class DeviceService(ThingsBoardService):
    """设备管理服务"""
    
    def get_devices(self, params: DeviceSearchParams) -> dict:
        """获取设备列表"""
        try:
            client = self.get_authenticated_client()
            # 获取设备信息列表
            page_data = client.get_tenant_device_infos(
                page_size=params.page_size,
                page=params.page,
                type=params.type,
                text_search=params.text_search,
                sort_property=params.sort_property,
                sort_order=params.sort_order,
                active=params.active
            )

            # 转换为我们的模型格式
            devices = []
            for device_info in page_data.data:
                devices.append({
                    "id": device_info.id.id,
                    "name": device_info.name,
                    "label": device_info.label,
                    "type": device_info.type,
                    "device_profile_name": device_info.device_profile_name,
                    "customer_title": device_info.customer_title,
                    "active": device_info.active,
                    "created_time": device_info.created_time,
                    "additional_info": device_info.additional_info
                })

            return {
                "success": True,
                "data": {
                    "devices": devices,
                    "total_pages": getattr(page_data, 'total_pages', 0),
                    "total_elements": getattr(page_data, 'total_elements', len(devices)),
                    "has_next": getattr(page_data, 'has_next', False),
                    "page": getattr(page_data, 'page', 0),
                    "page_size": getattr(page_data, 'page_size', len(devices))
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取设备列表")
        except Exception as e:
            return self.handle_exception(e, "获取设备列表")
    
    def get_device_by_id(self, device_id: str) -> dict:
        """根据ID获取设备"""
        try:
            client = self.get_authenticated_client()
            device = client.get_device_by_id(device_id)

            return {
                "success": True,
                "data": {
                    "id": device.id.id,
                    "name": device.name,
                    "label": device.label,
                    "type": device.type,
                    "device_profile_id": device.device_profile_id.id,
                    "customer_id": device.customer_id.id if device.customer_id else None,
                    "created_time": device.created_time,
                    "additional_info": device.additional_info
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取设备详情")
        except Exception as e:
            return self.handle_exception(e, "获取设备详情")
    
    def create_device(self, request: CreateDeviceRequest) -> dict:
        """创建设备"""
        try:
            client = self.get_authenticated_client()
            # 获取默认设备配置文件（如果未指定）
            device_profile_id = request.device_profile_id
            if not device_profile_id:
                default_profile = client.get_default_device_profile_info()
                device_profile_id = default_profile.id.id

            # 创建设备对象
            device = Device(
                name=request.name,
                label=request.label,
                device_profile_id={"entityType": "DEVICE_PROFILE", "id": device_profile_id},
                type=request.type,
                additional_info=request.additional_info
            )

            # 保存设备
            saved_device = client.save_device(device, access_token=request.access_token)

            return {
                "success": True,
                "data": {
                    "id": saved_device.id.id,
                    "name": saved_device.name,
                    "label": saved_device.label,
                    "type": saved_device.type,
                    "device_profile_id": saved_device.device_profile_id.id,
                    "created_time": saved_device.created_time,
                    "additional_info": saved_device.additional_info
                },
                "message": f"设备 '{request.name}' 创建成功"
            }
        except ApiException as e:
            return self.handle_api_exception(e, "创建设备")
        except Exception as e:
            return self.handle_exception(e, "创建设备")
    
    def update_device(self, device_id: str, request: UpdateDeviceRequest) -> dict:
        """更新设备"""
        try:
            client = self.get_authenticated_client()
            # 先获取现有设备
            existing_device = client.get_device_by_id(device_id)

            # 更新字段
            if request.name is not None:
                existing_device.name = request.name
            if request.label is not None:
                existing_device.label = request.label
            if request.type is not None:
                existing_device.type = request.type
            if request.device_profile_id is not None:
                existing_device.device_profile_id = {
                    "entityType": "DEVICE_PROFILE",
                    "id": request.device_profile_id
                }
            if request.additional_info is not None:
                existing_device.additional_info = request.additional_info

            # 保存更新
            updated_device = client.save_device(existing_device)

            return {
                "success": True,
                "data": {
                    "id": updated_device.id.id,
                    "name": updated_device.name,
                    "label": updated_device.label,
                    "type": updated_device.type,
                    "device_profile_id": updated_device.device_profile_id.id,
                    "created_time": updated_device.created_time,
                    "additional_info": updated_device.additional_info
                },
                "message": f"设备 '{updated_device.name}' 更新成功"
            }
        except ApiException as e:
            return self.handle_api_exception(e, "更新设备")
        except Exception as e:
            return self.handle_exception(e, "更新设备")
    
    def delete_device(self, device_id: str) -> dict:
        """删除设备"""
        try:
            client = self.get_authenticated_client()
            # 先获取设备名称用于消息
            device = client.get_device_by_id(device_id)
            device_name = device.name

            # 删除设备
            client.delete_device(device_id)

            return {
                "success": True,
                "message": f"设备 '{device_name}' 删除成功"
            }
        except ApiException as e:
            return self.handle_api_exception(e, "删除设备")
        except Exception as e:
            return self.handle_exception(e, "删除设备")
    
    def get_device_credentials(self, device_id: str) -> dict:
        """获取设备凭证"""
        try:
            client = self.get_authenticated_client()
            credentials = client.get_device_credentials_by_device_id(device_id)

            return {
                "success": True,
                "data": {
                    "id": credentials.id.id,
                    "device_id": credentials.device_id.id,
                    "credentials_type": credentials.credentials_type,
                    "credentials_id": credentials.credentials_id,
                    "credentials_value": credentials.credentials_value,
                    "created_time": credentials.created_time
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取设备凭证")
        except Exception as e:
            return self.handle_exception(e, "获取设备凭证")
    
    def get_device_profiles(self) -> dict:
        """获取设备配置文件列表"""
        try:
            client = self.get_authenticated_client()
            page_data = client.get_device_profiles(page_size=100, page=0)

            profiles = []
            for profile in page_data.data:
                profiles.append({
                    "id": profile.id.id,
                    "name": profile.name,
                    "type": profile.type,
                    "transport_type": profile.transport_type,
                    "description": profile.description,
                    "is_default": profile.default,
                    "created_time": profile.created_time
                })

            return {
                "success": True,
                "data": profiles
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取设备配置文件")
        except Exception as e:
            return self.handle_exception(e, "获取设备配置文件")
    
    def get_device_types(self) -> dict:
        """获取设备类型列表"""
        try:
            client = self.get_authenticated_client()
            device_types = client.get_device_types()

            types = [device_type.type for device_type in device_types]

            return {
                "success": True,
                "data": types
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取设备类型")
        except Exception as e:
            return self.handle_exception(e, "获取设备类型")


# 全局服务实例
_device_service = None


def get_device_service() -> DeviceService:
    """获取设备服务实例（单例模式）"""
    global _device_service
    if _device_service is None:
        _device_service = DeviceService()
    return _device_service
