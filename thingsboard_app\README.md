# ThingsBoard 全栈应用程序

一个基于ThingsBoard Community Edition的现代化全栈应用程序，使用ThingsBoard Python REST API客户端构建。

## 项目架构

```
thingsboard_app/
├── backend/                    # 后端API服务
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── config.py          # 配置管理
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务逻辑层
│   │   ├── api/               # API路由
│   │   └── utils/             # 工具函数
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/                   # React前端应用
│   ├── public/
│   ├── src/
│   │   ├── components/        # React组件
│   │   ├── services/          # API服务
│   │   ├── utils/             # 工具函数
│   │   └── App.js
│   ├── package.json
│   └── Dockerfile
├── docker-compose.yml         # 容器编排
├── .env.example              # 环境变量示例
└── README.md                 # 项目文档
```

## 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **tb-rest-client**: ThingsBoard Python REST客户端
- **Pydantic**: 数据验证和序列化
- **SQLAlchemy**: 数据库ORM（可选，用于本地数据存储）
- **Redis**: 缓存和会话管理
- **Uvicorn**: ASGI服务器

### 前端
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Material-UI**: UI组件库
- **Axios**: HTTP客户端
- **Chart.js**: 数据可视化
- **React Router**: 路由管理

### 开发工具
- **Docker**: 容器化部署
- **Docker Compose**: 多容器编排
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

## 核心功能

1. **设备管理**
   - 设备列表查看
   - 设备创建和编辑
   - 设备状态监控
   - 设备分组管理

2. **遥测数据**
   - 实时数据展示
   - 历史数据查询
   - 数据可视化图表
   - 数据导出功能

3. **用户管理**
   - 用户认证和授权
   - 用户信息管理
   - 权限控制



## 快速开始

### 前置要求
- Python 3.8+
- Node.js 16+
- Docker & Docker Compose
- ThingsBoard Community Edition实例

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd thingsboard_app
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，配置ThingsBoard连接信息
```

3. 使用Docker Compose启动
```bash
docker-compose up -d
```

4. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 开发模式

#### 后端开发
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端开发
```bash
cd frontend
npm install
npm start
```

### ThingsBoard设置

确保您的ThingsBoard实例正在运行并可访问：

1. **默认访问地址**: http://localhost:8080
2. **默认租户管理员账户**:
   - 用户名: <EMAIL>
   - 密码: tenant
3. **系统管理员账户**:
   - 用户名: <EMAIL>
   - 密码: sysadmin

### 功能演示

#### 1. 创建测试设备
```bash
# 使用API创建设备
curl -X POST "http://localhost:8000/api/devices" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "温度传感器01",
    "label": "办公室温度传感器",
    "type": "temperature_sensor"
  }'
```

#### 2. 发送遥测数据
```bash
# 发送测试数据
curl -X POST "http://localhost:8000/api/telemetry/{device_id}/telemetry" \
  -H "Content-Type: application/json" \
  -d '{
    "scope": "DEVICE",
    "data": {
      "temperature": 23.5,
      "humidity": 65.2,
      "timestamp": 1640995200000
    }
  }'
```

## 配置说明

### 环境变量
- `THINGSBOARD_URL`: ThingsBoard服务器地址
- `THINGSBOARD_USERNAME`: ThingsBoard用户名
- `THINGSBOARD_PASSWORD`: ThingsBoard密码
- `REDIS_URL`: Redis连接地址
- `SECRET_KEY`: JWT密钥

### ThingsBoard配置
确保您的ThingsBoard实例已正确配置并可访问。

## API文档

后端提供完整的RESTful API，支持：
- 设备管理 (`/api/devices`)
- 遥测数据 (`/api/telemetry`)
- 用户管理 (`/api/users`)
- 认证授权 (`/api/auth`)

详细API文档可在 `http://localhost:8000/docs` 查看。

## 扩展性设计

本应用采用模块化设计，便于扩展：

1. **插件化架构**: 支持添加新的ThingsBoard功能模块
2. **配置驱动**: 通过配置文件轻松添加新功能
3. **微服务就绪**: 可轻松拆分为微服务架构
4. **API优先**: 前后端分离，支持多种前端技术

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用Apache 2.0许可证。
