"""
遥测数据API路由
"""
from fastapi import APIRouter, HTTPException, Query, Path
from typing import Optional, List
from ..models.common import BaseResponse, ResponseStatus
from ..models.telemetry import (
    TelemetryRequest, SaveTelemetryRequest, SaveAttributesRequest,
    LatestTelemetry, TimeseriesData, AttributeData
)
from ..services.telemetry_service import get_telemetry_service

router = APIRouter(prefix="/telemetry", tags=["遥测数据"])


@router.get("/{entity_id}/latest", response_model=BaseResponse, summary="获取最新遥测数据")
async def get_latest_telemetry(
    entity_id: str = Path(..., description="实体ID"),
    keys: Optional[str] = Query(None, description="数据键列表，逗号分隔")
):
    """
    获取设备的最新遥测数据
    """
    telemetry_service = get_telemetry_service()
    
    # 解析keys参数
    key_list = keys.split(",") if keys else None
    
    result = telemetry_service.get_latest_telemetry(entity_id, key_list)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取最新遥测数据成功",
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 500),
            detail=result.get("error", "获取最新遥测数据失败")
        )


@router.get("/{entity_id}/timeseries", response_model=BaseResponse, summary="获取时间序列数据")
async def get_timeseries_data(
    entity_id: str = Path(..., description="实体ID"),
    keys: Optional[str] = Query(None, description="数据键列表，逗号分隔"),
    start_time: Optional[int] = Query(None, description="开始时间戳（毫秒）"),
    end_time: Optional[int] = Query(None, description="结束时间戳（毫秒）"),
    interval: Optional[int] = Query(None, description="聚合间隔（毫秒）"),
    limit: Optional[int] = Query(100, description="数据点限制"),
    agg: Optional[str] = Query("NONE", description="聚合类型"),
    use_strict_data_types: Optional[bool] = Query(False, description="使用严格数据类型")
):
    """
    获取设备的时间序列数据
    """
    telemetry_service = get_telemetry_service()
    
    # 构建请求参数
    request = TelemetryRequest(
        entity_id=entity_id,
        keys=keys.split(",") if keys else None,
        start_time=start_time,
        end_time=end_time,
        interval=interval,
        limit=limit,
        agg=agg,
        use_strict_data_types=use_strict_data_types
    )
    
    result = telemetry_service.get_timeseries_data(request)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取时间序列数据成功",
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 500),
            detail=result.get("error", "获取时间序列数据失败")
        )


@router.post("/{entity_id}/telemetry", response_model=BaseResponse, summary="保存遥测数据")
async def save_telemetry(
    entity_id: str = Path(..., description="实体ID"),
    request: SaveTelemetryRequest = None
):
    """
    保存设备遥测数据
    """
    telemetry_service = get_telemetry_service()
    
    # 设置实体ID
    request.entity_id = entity_id
    
    result = telemetry_service.save_telemetry(request)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message=result.get("message", "遥测数据保存成功"),
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 400),
            detail=result.get("error", "保存遥测数据失败")
        )


@router.get("/{entity_id}/attributes", response_model=BaseResponse, summary="获取属性数据")
async def get_attributes(
    entity_id: str = Path(..., description="实体ID"),
    scope: str = Query("SERVER_SCOPE", description="属性范围"),
    keys: Optional[str] = Query(None, description="属性键列表，逗号分隔")
):
    """
    获取设备属性数据
    """
    telemetry_service = get_telemetry_service()
    
    # 解析keys参数
    key_list = keys.split(",") if keys else None
    
    result = telemetry_service.get_attributes(entity_id, scope, key_list)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取属性数据成功",
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 500),
            detail=result.get("error", "获取属性数据失败")
        )


@router.post("/{entity_id}/attributes", response_model=BaseResponse, summary="保存属性数据")
async def save_attributes(
    entity_id: str = Path(..., description="实体ID"),
    request: SaveAttributesRequest = None
):
    """
    保存设备属性数据
    """
    telemetry_service = get_telemetry_service()
    
    # 设置实体ID
    request.entity_id = entity_id
    
    result = telemetry_service.save_attributes(request)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message=result.get("message", "属性数据保存成功"),
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 400),
            detail=result.get("error", "保存属性数据失败")
        )


@router.get("/{entity_id}/keys/telemetry", response_model=BaseResponse, summary="获取遥测数据键")
async def get_telemetry_keys(
    entity_id: str = Path(..., description="实体ID")
):
    """
    获取设备的遥测数据键列表
    """
    telemetry_service = get_telemetry_service()
    
    result = telemetry_service.get_telemetry_keys(entity_id)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取遥测数据键成功",
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 500),
            detail=result.get("error", "获取遥测数据键失败")
        )


@router.get("/{entity_id}/keys/attributes", response_model=BaseResponse, summary="获取属性键")
async def get_attribute_keys(
    entity_id: str = Path(..., description="实体ID")
):
    """
    获取设备的属性键列表
    """
    telemetry_service = get_telemetry_service()
    
    result = telemetry_service.get_attribute_keys(entity_id)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message="获取属性键成功",
            data=result.get("data")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 500),
            detail=result.get("error", "获取属性键失败")
        )


@router.delete("/{entity_id}/timeseries", response_model=BaseResponse, summary="删除时间序列数据")
async def delete_timeseries(
    entity_id: str = Path(..., description="实体ID"),
    keys: str = Query(..., description="数据键列表，逗号分隔"),
    start_time: Optional[int] = Query(None, description="开始时间戳（毫秒）"),
    end_time: Optional[int] = Query(None, description="结束时间戳（毫秒）")
):
    """
    删除设备的时间序列数据
    """
    telemetry_service = get_telemetry_service()
    
    # 解析keys参数
    key_list = keys.split(",")
    
    result = telemetry_service.delete_timeseries(entity_id, key_list, start_time, end_time)
    
    if result.get("success"):
        return BaseResponse(
            status=ResponseStatus.SUCCESS,
            message=result.get("message", "删除时间序列数据成功")
        )
    else:
        raise HTTPException(
            status_code=result.get("status_code", 400),
            detail=result.get("error", "删除时间序列数据失败")
        )


# 仪表板功能已移除
