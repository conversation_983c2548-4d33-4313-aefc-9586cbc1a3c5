/**
 * 遥测数据查看组件
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
} from '@mui/material';
import {
  DateTimePicker,
} from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Line } from 'react-chartjs-2';
import { deviceApi, telemetryApi, apiUtils } from '../../services/api';

function TelemetryView() {
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState('');
  const [telemetryKeys, setTelemetryKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [startTime, setStartTime] = useState(new Date(Date.now() - 24 * 60 * 60 * 1000));
  const [endTime, setEndTime] = useState(new Date());
  const [telemetryData, setTelemetryData] = useState([]);
  const [latestData, setLatestData] = useState({});
  const [attributes, setAttributes] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 加载设备列表
  const loadDevices = async () => {
    try {
      const response = await deviceApi.getDevices({ page_size: 100 });
      const data = apiUtils.handleResponse(response);
      setDevices(data.devices || []);
    } catch (err) {
      console.error('加载设备列表失败:', err);
      setError(err.message);
    }
  };

  // 加载遥测数据键
  const loadTelemetryKeys = async (deviceId) => {
    try {
      const response = await telemetryApi.getTelemetryKeys(deviceId);
      const keys = apiUtils.handleResponse(response);
      setTelemetryKeys(keys || []);
      setSelectedKeys(keys?.slice(0, 3) || []); // 默认选择前3个键
    } catch (err) {
      console.error('加载遥测数据键失败:', err);
      setTelemetryKeys([]);
    }
  };

  // 加载遥测数据
  const loadTelemetryData = async () => {
    if (!selectedDevice || selectedKeys.length === 0) return;

    try {
      setLoading(true);
      setError(null);

      // 获取时间序列数据
      const timeseriesResponse = await telemetryApi.getTimeseriesData(selectedDevice, {
        keys: selectedKeys.join(','),
        start_time: startTime.getTime(),
        end_time: endTime.getTime(),
        limit: 1000,
      });
      const timeseriesData = apiUtils.handleResponse(timeseriesResponse);
      setTelemetryData(timeseriesData || []);

      // 获取最新数据
      const latestResponse = await telemetryApi.getLatestTelemetry(selectedDevice, selectedKeys);
      const latest = apiUtils.handleResponse(latestResponse);
      setLatestData(latest || {});

      // 获取属性数据
      const attributesResponse = await telemetryApi.getAttributes(selectedDevice);
      const attrs = apiUtils.handleResponse(attributesResponse);
      setAttributes(attrs || {});
    } catch (err) {
      console.error('加载遥测数据失败:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 设备选择变化
  const handleDeviceChange = (event) => {
    const deviceId = event.target.value;
    setSelectedDevice(deviceId);
    setTelemetryData([]);
    setLatestData({});
    setAttributes({});
    
    if (deviceId) {
      loadTelemetryKeys(deviceId);
    } else {
      setTelemetryKeys([]);
      setSelectedKeys([]);
    }
  };

  // 数据键选择变化
  const handleKeysChange = (event) => {
    setSelectedKeys(event.target.value);
  };

  // 生成图表数据
  const generateChartData = () => {
    if (!telemetryData || telemetryData.length === 0) return null;

    const datasets = telemetryData.map((series, index) => {
      const colors = [
        'rgb(75, 192, 192)',
        'rgb(255, 99, 132)',
        'rgb(54, 162, 235)',
        'rgb(255, 205, 86)',
        'rgb(153, 102, 255)',
      ];
      
      return {
        label: series.key,
        data: series.data.map(point => ({
          x: new Date(point.ts),
          y: point.value,
        })),
        borderColor: colors[index % colors.length],
        backgroundColor: colors[index % colors.length] + '20',
        tension: 0.1,
      };
    });

    return {
      datasets,
    };
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: '遥测数据趋势图',
      },
    },
    scales: {
      x: {
        type: 'time',
        time: {
          displayFormats: {
            minute: 'HH:mm',
            hour: 'MM-dd HH:mm',
          },
        },
      },
      y: {
        beginAtZero: false,
      },
    },
  };

  useEffect(() => {
    loadDevices();
  }, []);

  useEffect(() => {
    if (selectedDevice && selectedKeys.length > 0) {
      loadTelemetryData();
    }
  }, [selectedDevice, selectedKeys, startTime, endTime]);

  const chartData = generateChartData();

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          遥测数据查看
        </Typography>

        {/* 控制面板 */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>选择设备</InputLabel>
                  <Select
                    value={selectedDevice}
                    onChange={handleDeviceChange}
                    label="选择设备"
                  >
                    <MenuItem value="">
                      <em>请选择设备</em>
                    </MenuItem>
                    {devices.map((device) => (
                      <MenuItem key={device.id} value={device.id}>
                        {device.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={3}>
                <FormControl fullWidth disabled={!selectedDevice}>
                  <InputLabel>数据键</InputLabel>
                  <Select
                    multiple
                    value={selectedKeys}
                    onChange={handleKeysChange}
                    label="数据键"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {telemetryKeys.map((key) => (
                      <MenuItem key={key} value={key}>
                        {key}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={2}>
                <DateTimePicker
                  label="开始时间"
                  value={startTime}
                  onChange={setStartTime}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>

              <Grid item xs={12} md={2}>
                <DateTimePicker
                  label="结束时间"
                  value={endTime}
                  onChange={setEndTime}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>

              <Grid item xs={12} md={2}>
                <Button
                  variant="contained"
                  onClick={loadTelemetryData}
                  disabled={!selectedDevice || selectedKeys.length === 0 || loading}
                  fullWidth
                >
                  {loading ? <CircularProgress size={20} /> : '查询'}
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* 最新数据 */}
        {Object.keys(latestData).length > 0 && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最新数据
              </Typography>
              <Grid container spacing={2}>
                {Object.entries(latestData).map(([key, data]) => (
                  <Grid item xs={12} sm={6} md={4} key={key}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography color="textSecondary" gutterBottom>
                          {key}
                        </Typography>
                        <Typography variant="h5">
                          {data.value}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {apiUtils.formatTimestamp(data.ts)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* 图表 */}
        {chartData && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Line data={chartData} options={chartOptions} />
            </CardContent>
          </Card>
        )}

        {/* 属性数据 */}
        {Object.keys(attributes).length > 0 && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                设备属性
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>属性名</TableCell>
                      <TableCell>属性值</TableCell>
                      <TableCell>最后更新时间</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Object.entries(attributes).map(([key, data]) => (
                      <TableRow key={key}>
                        <TableCell>{key}</TableCell>
                        <TableCell>{JSON.stringify(data.value)}</TableCell>
                        <TableCell>
                          {data.last_update_ts ? 
                            apiUtils.formatTimestamp(data.last_update_ts) : 
                            '未知'
                          }
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {/* 数据表格 */}
        {telemetryData.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                历史数据
              </Typography>
              {telemetryData.map((series) => (
                <Box key={series.key} sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {series.key}
                  </Typography>
                  <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell>时间</TableCell>
                          <TableCell>值</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {series.data.slice(0, 100).map((point, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              {apiUtils.formatTimestamp(point.ts)}
                            </TableCell>
                            <TableCell>{point.value}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              ))}
            </CardContent>
          </Card>
        )}

        {selectedDevice && selectedKeys.length > 0 && telemetryData.length === 0 && !loading && (
          <Card>
            <CardContent>
              <Typography variant="h6" align="center" color="textSecondary">
                暂无遥测数据
              </Typography>
              <Typography variant="body2" align="center" color="textSecondary" mt={1}>
                所选时间范围内没有找到数据
              </Typography>
            </CardContent>
          </Card>
        )}
      </Box>
    </LocalizationProvider>
  );
}

export default TelemetryView;
