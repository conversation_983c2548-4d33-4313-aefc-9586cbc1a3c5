{"short_name": "ThingsBoard App", "name": "ThingsBoard 全栈应用程序", "description": "基于ThingsBoard Community Edition的现代化全栈应用程序", "icons": [{"src": "favicon.svg", "sizes": "any", "type": "image/svg+xml"}], "start_url": ".", "display": "standalone", "theme_color": "#1976d2", "background_color": "#ffffff", "orientation": "portrait-primary", "scope": "/", "lang": "zh-CN", "categories": ["productivity", "utilities"], "screenshots": []}