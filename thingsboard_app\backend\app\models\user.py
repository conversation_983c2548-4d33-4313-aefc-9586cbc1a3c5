"""
用户相关数据模型
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from enum import Enum
from .common import EntityId


class Authority(str, Enum):
    """权限枚举"""
    SYS_ADMIN = "SYS_ADMIN"
    TENANT_ADMIN = "TENANT_ADMIN"
    CUSTOMER_USER = "CUSTOMER_USER"


class User(BaseModel):
    """用户模型"""
    id: Optional[EntityId] = Field(default=None, description="用户ID")
    email: EmailStr = Field(description="邮箱地址")
    first_name: Optional[str] = Field(default=None, description="名字")
    last_name: Optional[str] = Field(default=None, description="姓氏")
    authority: Authority = Field(description="权限级别")
    tenant_id: Optional[EntityId] = Field(default=None, description="租户ID")
    customer_id: Optional[EntityId] = Field(default=None, description="客户ID")
    phone: Optional[str] = Field(default=None, description="电话号码")
    additional_info: Optional[Dict[str, Any]] = Field(default=None, description="附加信息")
    created_time: Optional[datetime] = Field(default=None, description="创建时间")


class CreateUserRequest(BaseModel):
    """创建用户请求模型"""
    email: EmailStr = Field(description="邮箱地址")
    first_name: str = Field(description="名字")
    last_name: str = Field(description="姓氏")
    authority: Authority = Field(description="权限级别")
    phone: Optional[str] = Field(default=None, description="电话号码")
    additional_info: Optional[Dict[str, Any]] = Field(default=None, description="附加信息")
    send_activation_mail: bool = Field(default=True, description="是否发送激活邮件")


class UpdateUserRequest(BaseModel):
    """更新用户请求模型"""
    email: Optional[EmailStr] = Field(default=None, description="邮箱地址")
    first_name: Optional[str] = Field(default=None, description="名字")
    last_name: Optional[str] = Field(default=None, description="姓氏")
    phone: Optional[str] = Field(default=None, description="电话号码")
    additional_info: Optional[Dict[str, Any]] = Field(default=None, description="附加信息")


class UserSearchParams(BaseModel):
    """用户搜索参数"""
    page: int = Field(default=0, ge=0, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    text_search: Optional[str] = Field(default=None, description="文本搜索")
    sort_property: Optional[str] = Field(default="email", description="排序字段")
    sort_order: Optional[str] = Field(default="ASC", description="排序方向")


class UserProfile(BaseModel):
    """用户配置文件模型"""
    user_id: str = Field(description="用户ID")
    email: str = Field(description="邮箱地址")
    first_name: str = Field(description="名字")
    last_name: str = Field(description="姓氏")
    full_name: str = Field(description="全名")
    authority: str = Field(description="权限级别")
    tenant_name: Optional[str] = Field(default=None, description="租户名称")
    customer_name: Optional[str] = Field(default=None, description="客户名称")
    phone: Optional[str] = Field(default=None, description="电话号码")
    avatar_url: Optional[str] = Field(default=None, description="头像地址")
    language: Optional[str] = Field(default="zh_CN", description="语言设置")
    timezone: Optional[str] = Field(default="Asia/Shanghai", description="时区设置")
    theme: Optional[str] = Field(default="light", description="主题设置")
    created_time: datetime = Field(description="创建时间")
    last_login_time: Optional[datetime] = Field(default=None, description="最后登录时间")


class UserActivity(BaseModel):
    """用户活动模型"""
    user_id: str = Field(description="用户ID")
    action: str = Field(description="操作类型")
    entity_type: Optional[str] = Field(default=None, description="实体类型")
    entity_id: Optional[str] = Field(default=None, description="实体ID")
    entity_name: Optional[str] = Field(default=None, description="实体名称")
    action_data: Optional[Dict[str, Any]] = Field(default=None, description="操作数据")
    action_status: str = Field(description="操作状态")
    action_failure_details: Optional[str] = Field(default=None, description="失败详情")
    created_time: datetime = Field(description="创建时间")


class UserSettings(BaseModel):
    """用户设置模型"""
    user_id: str = Field(description="用户ID")
    language: str = Field(default="zh_CN", description="语言设置")
    timezone: str = Field(default="Asia/Shanghai", description="时区设置")
    theme: str = Field(default="light", description="主题设置")

    notification_settings: Optional[Dict[str, Any]] = Field(default=None, description="通知设置")
    privacy_settings: Optional[Dict[str, Any]] = Field(default=None, description="隐私设置")


class UserStatistics(BaseModel):
    """用户统计模型"""
    total_users: int = Field(description="用户总数")
    active_users: int = Field(description="活跃用户数")
    users_by_authority: Dict[str, int] = Field(description="按权限分组的用户数")
    new_users_today: int = Field(description="今日新增用户数")
    new_users_this_week: int = Field(description="本周新增用户数")
    new_users_this_month: int = Field(description="本月新增用户数")
    last_login_distribution: Dict[str, int] = Field(description="最后登录时间分布")


class PasswordPolicy(BaseModel):
    """密码策略模型"""
    min_length: int = Field(default=8, description="最小长度")
    max_length: int = Field(default=128, description="最大长度")
    require_uppercase: bool = Field(default=True, description="需要大写字母")
    require_lowercase: bool = Field(default=True, description="需要小写字母")
    require_digits: bool = Field(default=True, description="需要数字")
    require_special_chars: bool = Field(default=True, description="需要特殊字符")
    password_expiry_days: Optional[int] = Field(default=None, description="密码过期天数")
    password_history_size: int = Field(default=5, description="密码历史记录大小")


class UserInvitation(BaseModel):
    """用户邀请模型"""
    email: EmailStr = Field(description="邮箱地址")
    authority: Authority = Field(description="权限级别")
    message: Optional[str] = Field(default=None, description="邀请消息")
    expires_at: Optional[datetime] = Field(default=None, description="过期时间")
    created_by: str = Field(description="创建者ID")
    created_time: datetime = Field(description="创建时间")
