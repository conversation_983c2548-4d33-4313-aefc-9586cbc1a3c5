"""
ThingsBoard服务基础类
"""
import logging
from typing import Optional
from contextlib import contextmanager
from tb_rest_client.rest_client_ce import RestClientCE
from tb_rest_client.rest import ApiException
from ..config import get_settings

logger = logging.getLogger(__name__)


class ThingsBoardService:
    """ThingsBoard服务基础类"""

    _instance = None
    _client_instance = None
    _authenticated = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ThingsBoardService, cls).__new__(cls)
            cls._instance.settings = get_settings()
        return cls._instance

    def __init__(self):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        self.settings = get_settings()
        self._initialized = True

    @property
    def client(self) -> RestClientCE:
        """获取ThingsBoard客户端实例（单例）"""
        if ThingsBoardService._client_instance is None:
            ThingsBoardService._client_instance = RestClientCE(base_url=self.settings.thingsboard_url)
        return ThingsBoardService._client_instance
    
    def authenticate(self) -> bool:
        """认证到ThingsBoard"""
        try:
            if not ThingsBoardService._authenticated:
                self.client.login(
                    username=self.settings.thingsboard_username,
                    password=self.settings.thingsboard_password
                )
                ThingsBoardService._authenticated = True
                logger.info("成功认证到ThingsBoard")
            return True
        except ApiException as e:
            logger.error(f"ThingsBoard认证失败: {e}")
            ThingsBoardService._authenticated = False
            return False
        except Exception as e:
            logger.error(f"ThingsBoard认证异常: {e}")
            ThingsBoardService._authenticated = False
            return False

    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return ThingsBoardService._authenticated
    
    def get_authenticated_client(self):
        """获取已认证的客户端"""
        try:
            if not self.authenticate():
                raise Exception("无法认证到ThingsBoard")

            return self.client
        except Exception as e:
            logger.error(f"获取认证客户端失败: {e}")
            raise
    
    def handle_api_exception(self, e: ApiException, operation: str = "操作") -> dict:
        """处理API异常"""
        error_msg = f"{operation}失败"
        status_code = getattr(e, 'status', 500)
        
        try:
            # 尝试解析错误响应
            if hasattr(e, 'body') and e.body:
                error_detail = e.body
                if isinstance(error_detail, str):
                    error_msg = f"{operation}失败: {error_detail}"
                elif isinstance(error_detail, dict):
                    error_msg = f"{operation}失败: {error_detail.get('message', str(e))}"
        except Exception:
            error_msg = f"{operation}失败: {str(e)}"
        
        logger.error(f"API异常 - {error_msg} (状态码: {status_code})")
        
        return {
            "success": False,
            "error": error_msg,
            "status_code": status_code,
            "details": str(e)
        }
    
    def handle_exception(self, e: Exception, operation: str = "操作") -> dict:
        """处理通用异常"""
        error_msg = f"{operation}异常: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return {
            "success": False,
            "error": error_msg,
            "status_code": 500,
            "details": str(e)
        }
    
    def check_connection(self) -> dict:
        """检查ThingsBoard连接状态"""
        try:
            client = self.get_authenticated_client()
            # 尝试获取用户信息来验证连接
            user_info = client.get_user()
            return {
                "success": True,
                "status": "connected",
                "user": {
                    "email": user_info.email,
                    "authority": user_info.authority,
                    "tenant_id": str(user_info.tenant_id.id) if user_info.tenant_id else None
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "连接检查")
        except Exception as e:
            return self.handle_exception(e, "连接检查")
    
    def get_system_info(self) -> dict:
        """获取系统信息"""
        try:
            client = self.get_authenticated_client()
            system_info = client.get_system_info()
            return {
                "success": True,
                "data": {
                    "version": system_info.version,
                    "platform": system_info.platform,
                    "build_time": system_info.build_time
                }
            }
        except ApiException as e:
            return self.handle_api_exception(e, "获取系统信息")
        except Exception as e:
            return self.handle_exception(e, "获取系统信息")
    
    def logout(self):
        """登出"""
        try:
            if self._client and self._authenticated:
                self._client.logout()
                self._authenticated = False
                logger.info("已从ThingsBoard登出")
        except Exception as e:
            logger.warning(f"登出时发生异常: {e}")
        finally:
            self._authenticated = False
    
    def __del__(self):
        """析构函数，确保清理资源"""
        try:
            self.logout()
        except Exception:
            pass


# 全局服务实例
_thingsboard_service = None


def get_thingsboard_service() -> ThingsBoardService:
    """获取ThingsBoard服务实例（单例模式）"""
    global _thingsboard_service
    if _thingsboard_service is None:
        _thingsboard_service = ThingsBoardService()
    return _thingsboard_service
