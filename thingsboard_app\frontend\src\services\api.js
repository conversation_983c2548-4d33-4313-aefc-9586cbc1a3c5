/**
 * API服务模块
 */
import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token（如果有的话）
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('响应错误:', error.response?.status, error.response?.data);
    
    // 处理认证错误
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// 健康检查API
export const healthApi = {
  // 检查应用健康状态
  checkHealth: () => api.get('/health'),
  
  // 检查ThingsBoard连接
  checkThingsBoard: () => api.get('/health/thingsboard'),
  
  // 获取系统信息
  getSystemInfo: () => api.get('/health/system-info'),
};

// 设备管理API
export const deviceApi = {
  // 获取设备列表
  getDevices: (params = {}) => api.get('/devices', { params }),
  
  // 获取设备详情
  getDevice: (deviceId) => api.get(`/devices/${deviceId}`),
  
  // 创建设备
  createDevice: (deviceData) => api.post('/devices', deviceData),
  
  // 更新设备
  updateDevice: (deviceId, deviceData) => api.put(`/devices/${deviceId}`, deviceData),
  
  // 删除设备
  deleteDevice: (deviceId) => api.delete(`/devices/${deviceId}`),
  
  // 获取设备凭证
  getDeviceCredentials: (deviceId) => api.get(`/devices/${deviceId}/credentials`),
  
  // 获取设备配置文件列表
  getDeviceProfiles: () => api.get('/devices/profiles/list'),
  
  // 获取设备类型列表
  getDeviceTypes: () => api.get('/devices/types/list'),
};

// 遥测数据API
export const telemetryApi = {
  // 获取最新遥测数据
  getLatestTelemetry: (entityId, keys = null) => {
    const params = keys ? { keys: keys.join(',') } : {};
    return api.get(`/telemetry/${entityId}/latest`, { params });
  },
  
  // 获取时间序列数据
  getTimeseriesData: (entityId, params = {}) => 
    api.get(`/telemetry/${entityId}/timeseries`, { params }),
  
  // 保存遥测数据
  saveTelemetry: (entityId, telemetryData) => 
    api.post(`/telemetry/${entityId}/telemetry`, telemetryData),
  
  // 获取属性数据
  getAttributes: (entityId, scope = 'SERVER_SCOPE', keys = null) => {
    const params = { scope };
    if (keys) params.keys = keys.join(',');
    return api.get(`/telemetry/${entityId}/attributes`, { params });
  },
  
  // 保存属性数据
  saveAttributes: (entityId, attributeData) => 
    api.post(`/telemetry/${entityId}/attributes`, attributeData),
  
  // 获取遥测数据键
  getTelemetryKeys: (entityId) => 
    api.get(`/telemetry/${entityId}/keys/telemetry`),
  
  // 获取属性键
  getAttributeKeys: (entityId) => 
    api.get(`/telemetry/${entityId}/keys/attributes`),
  
  // 删除时间序列数据
  deleteTimeseries: (entityId, keys, startTime = null, endTime = null) => {
    const params = { keys: keys.join(',') };
    if (startTime) params.start_time = startTime;
    if (endTime) params.end_time = endTime;
    return api.delete(`/telemetry/${entityId}/timeseries`, { params });
  },
  

};

// 通用API工具函数
export const apiUtils = {
  // 处理API响应
  handleResponse: (response) => {
    if (response.data.status === 'success') {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '请求失败');
    }
  },
  
  // 处理API错误
  handleError: (error) => {
    if (error.response) {
      // 服务器响应错误
      const message = error.response.data?.message || error.response.data?.detail || '服务器错误';
      throw new Error(message);
    } else if (error.request) {
      // 网络错误
      throw new Error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      throw new Error(error.message || '未知错误');
    }
  },
  
  // 格式化时间戳
  formatTimestamp: (timestamp) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  },
  
  // 生成时间范围
  getTimeRange: (hours) => {
    const endTime = Date.now();
    const startTime = endTime - (hours * 60 * 60 * 1000);
    return { startTime, endTime };
  },
};

export default api;
